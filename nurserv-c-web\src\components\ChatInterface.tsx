import React, { useEffect, useState, useRef, useCallback } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Loader2,
  MessageSquare,
  Send,
  AlertCircle,
  ArrowLeft,
} from 'lucide-react';
import useWebSocketChatApi from '@/hooks/useWebSocketChatApi';
import { Message } from '@/store/api/chatApiSlice';
import { format } from 'date-fns';
import { useToast } from '@/hooks/use-toast';

import { useMarkAsRead } from '@/hooks/useMarkAsRead';

const ChatInterface: React.FC = () => {
  const { conversationId } = useParams<{ conversationId: string }>();
  const location = useLocation();
  const navigate = useNavigate();

  const nurseName = location.state?.nurseName || 'Nurse';
  const nurseId = location.state?.nurseId;

  const [newMessage, setNewMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [typingTimeout, setTypingTimeout] = useState<NodeJS.Timeout | null>(
    null
  );
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();

  const userId = localStorage.getItem('userId') || '';
  const userGivenName = localStorage.getItem('userGivenName') || 'Patient';
  const userType = 'patient';

  // Initialize WebSocket Chat API
  const webSocketChatApi = useWebSocketChatApi();

  const { markAsReadImmediately, debouncedMarkAsRead, setupMessageObserver } =
    useMarkAsRead({
      conversationId,
      userId,
      debounceMs: 1000,
    });

  const {
    data: _conversationData,
    isLoading: isLoadingConversation,
    error: conversationError,
  } = webSocketChatApi.useGetConversationQuery(conversationId || '', {
    skip: !conversationId,
  });

  const {
    data: messagesData,
    isLoading: isLoadingMessages,
    error: messagesError,
  } = webSocketChatApi.useGetMessagesQuery(
    { conversationId: conversationId || '', page: 1, limit: 50 },
    { skip: !conversationId }
  );

  // Messages are now managed by the WebSocket API
  const messages = conversationId && messagesData?.data?.messages ? messagesData.data.messages : [];

  // Get WebSocket methods from the chat API
  const {
    webSocketStatus: wsStatus,
    sendTextMessage,
    sendTypingIndicator,
    sendReadReceipt,
    joinConversation,
    leaveConversation,
    typingUsers,
  } = webSocketChatApi;

  function handleWebSocketMessage(message: WebSocketMessage) {
    if (
      message.type === 'TEXT_MESSAGE' &&
      message.conversationId === conversationId
    ) {
      setMessages(prevMessages => {
        if (prevMessages.some(m => m.id === message.content)) {
          return prevMessages;
        }

        const newMsg: Message = {
          id: message.content || String(Date.now()),
          conversationId: message.conversationId || '',
          senderId: message.senderId || '',
          senderType: message.senderType || 'nurse',
          senderName: message.senderName || 'Unknown',
          content: message.content || '',
          type: 'text',
          status: 'sent',
          timestamp: message.timestamp || new Date().toISOString(),
        };

        return [...prevMessages, newMsg];
      });

      if (message.senderId !== userId && conversationId) {
        sendReadReceipt(conversationId);
        debouncedMarkAsRead();
      }
    }
  }

  useEffect(() => {
    if (conversationId && wsStatus.connected) {
      joinConversation(conversationId);
    }

    return () => {
      if (conversationId && wsStatus.connected) {
        leaveConversation(conversationId);
      }
    };
  }, [conversationId, wsStatus.connected, joinConversation, leaveConversation]);

  useEffect(() => {
    if (
      messagesData?.success &&
      (messagesData.data?.messages || messagesData.messages)
    ) {
      setMessages(messagesData.data?.messages || messagesData.messages || []);

      if (conversationId) {
        markAsReadImmediately();
      }
    }
  }, [messagesData, conversationId, markAsReadImmediately]);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });

    const messageElements = document.querySelectorAll('[data-message-id]');
    setupMessageObserver(messageElements);
  }, [messages, setupMessageObserver]);

  useEffect(() => {
    const handleFocus = () => {
      if (conversationId) {
        markAsReadImmediately();
      }
    };

    const handleVisibilityChange = () => {
      if (!document.hidden && conversationId) {
        markAsReadImmediately();
      }
    };

    window.addEventListener('focus', handleFocus);
    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      window.removeEventListener('focus', handleFocus);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [conversationId, markAsReadImmediately]);

  const handleTyping = useCallback(() => {
    if (!isTyping && conversationId) {
      setIsTyping(true);
      sendTypingIndicator(conversationId);
    }

    if (typingTimeout) {
      clearTimeout(typingTimeout);
    }

    const timeout = setTimeout(() => {
      setIsTyping(false);
    }, 3000);

    setTypingTimeout(timeout);
  }, [isTyping, conversationId, sendTypingIndicator, typingTimeout]);

  const handleSendMessage = async () => {
    if (!newMessage.trim() || !conversationId || isSendingMessage) return;

    try {
      const result = await sendMessage({
        conversationId,
        content: newMessage.trim(),
        type: 'text',
      }).unwrap();

      if (result.success) {
        sendTextMessage(conversationId, newMessage.trim());
        setNewMessage('');

        setIsTyping(false);
        if (typingTimeout) {
          clearTimeout(typingTimeout);
        }
      } else {
        toast({
          title: 'Error',
          description: 'Failed to send message',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error sending message:', error);
      toast({
        title: 'Error',
        description: 'Failed to send message',
        variant: 'destructive',
      });
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNewMessage(e.target.value);
    handleTyping();
  };

  const handleBack = () => {
    navigate('/chat');
  };

  const displayNurseName =
    location.state?.nurseName ||
    nurseInfoData?.nurse?.name ||
    nurseName ||
    'Nurse';

  const isLoading =
    isLoadingConversation ||
    isCreatingConversation ||
    (isLoadingMessages && !messages.length) ||
    isLoadingNurseInfo;

  if (isLoading) {
    return (
      <div className='min-h-screen flex items-center justify-center bg-white'>
        <div className='text-center'>
          <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-nursery-blue mx-auto'></div>
          <p className='mt-4 text-gray-600'>Loading conversation...</p>
        </div>
      </div>
    );
  }

  const error = conversationError || messagesError;
  if (error) {
    return (
      <div className='min-h-screen flex flex-col items-center justify-center bg-white p-4'>
        <div className='bg-white rounded-lg p-6 max-w-md w-full shadow-md'>
          <div className='flex items-center justify-between mb-4'>
            <h3 className='text-lg font-semibold text-red-600'>Chat Error</h3>
          </div>
          <div className='flex items-center text-red-600 mb-4'>
            <AlertCircle className='h-5 w-5 mr-2' />
            <p className='text-gray-600'>
              {error instanceof Error
                ? error.message
                : 'Failed to load chat. Please try again.'}
            </p>
          </div>
          <Button onClick={handleBack} className='w-full'>
            Back to Chats
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className='min-h-screen flex flex-col bg-white'>
      {}
      <header className='bg-white border-b border-gray-200 px-4 py-3 flex items-center sticky top-0 z-10'>
        <button
          onClick={handleBack}
          className='mr-3 p-2 hover:bg-gray-100 rounded-full'
          aria-label='Back to chats'
        >
          <ArrowLeft className='h-5 w-5 text-gray-600' />
        </button>
        <div className='flex items-center'>
          <div className='w-10 h-10 bg-nursery-blue rounded-full flex items-center justify-center mr-3'>
            <span className='text-white text-sm font-semibold'>
              {displayNurseName.charAt(0).toUpperCase()}
            </span>
          </div>
          <div>
            <h1 className='text-lg font-semibold text-gray-900'>
              {displayNurseName}
            </h1>
            <p className='text-sm text-gray-500'>Nurse</p>
          </div>
        </div>

        {}
        {!wsStatus.connected && (
          <div className='ml-auto flex items-center text-amber-600'>
            <AlertCircle className='h-4 w-4 mr-1' />
            <span className='text-xs'>Limited connectivity</span>
          </div>
        )}
      </header>

      {}
      <div
        className='flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50'
        aria-live='polite'
        aria-relevant='additions'
      >
        {messages.length === 0 ? (
          <div className='text-center text-gray-500 py-8'>
            <MessageSquare className='h-12 w-12 mx-auto mb-4 opacity-50' />
            <p>No messages yet. Start the conversation!</p>
          </div>
        ) : (
          messages.map(message => (
            <div
              key={message.id}
              data-message-id={message.id}
              data-sender-id={message.senderId}
              className={`flex ${message.senderId === userId ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                  message.senderId === userId
                    ? 'bg-nursery-blue text-white'
                    : 'bg-white border border-gray-200 text-gray-800'
                }`}
              >
                <p className='text-sm'>{message.content}</p>
                <p
                  className={`text-xs mt-1 ${message.senderId === userId ? 'text-blue-100' : 'text-gray-500'}`}
                  title={format(new Date(message.timestamp), 'PPpp')}
                >
                  {format(new Date(message.timestamp), 'h:mm a')}
                  {message.status === 'read' && message.senderId === userId && (
                    <span className='ml-1'>✓</span>
                  )}
                </p>
              </div>
            </div>
          ))
        )}

        {}
        {Object.values(typingUsers).map(
          user =>
            user.userId !== userId && (
              <div key={user.userId} className='flex justify-start'>
                <div className='bg-white border border-gray-200 text-gray-800 px-4 py-2 rounded-lg'>
                  <div className='flex space-x-1'>
                    <div
                      className='h-2 w-2 bg-gray-400 rounded-full animate-bounce'
                      style={{ animationDelay: '0ms' }}
                    ></div>
                    <div
                      className='h-2 w-2 bg-gray-400 rounded-full animate-bounce'
                      style={{ animationDelay: '300ms' }}
                    ></div>
                    <div
                      className='h-2 w-2 bg-gray-400 rounded-full animate-bounce'
                      style={{ animationDelay: '600ms' }}
                    ></div>
                  </div>
                  <p className='text-xs mt-1 text-gray-500'>
                    {user.userName} is typing...
                  </p>
                </div>
              </div>
            )
        )}

        <div ref={messagesEndRef} />
      </div>

      {}
      <div className='border-t p-4 bg-white sticky bottom-0'>
        <div className='flex space-x-2'>
          <Input
            value={newMessage}
            onChange={handleInputChange}
            onKeyDown={handleKeyPress}
            placeholder='Type your message...'
            disabled={isSendingMessage || !wsStatus.connected}
            className='flex-1'
            aria-label='Message input'
          />
          <Button
            onClick={handleSendMessage}
            disabled={
              !newMessage.trim() || isSendingMessage || !wsStatus.connected
            }
            className='bg-nursery-blue hover:bg-nursery-blue/90'
            aria-label='Send message'
          >
            {isSendingMessage ? (
              <Loader2 className='h-4 w-4 animate-spin' />
            ) : (
              <Send className='h-4 w-4' />
            )}
          </Button>
        </div>

        {}
        {!wsStatus.connected && wsStatus.error && (
          <p className='text-xs text-amber-600 mt-2 flex items-center'>
            <AlertCircle className='h-3 w-3 mr-1' />
            Connection issue. Messages may be delayed.
          </p>
        )}
      </div>
    </div>
  );
};

export default ChatInterface;
