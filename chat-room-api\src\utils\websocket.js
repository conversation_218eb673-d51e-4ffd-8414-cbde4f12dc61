const WebSocket = require('ws');
const { verifyAndGetUser, validateAndRefreshSession, extractToken } = require('./authUtils');

class WebSocketManager {
  constructor(server) {
    this.wss = new WebSocket.Server({ 
      server,
      // Add more verbose error handling for upgrade failures
      verifyClient: (info, cb) => {
        // Log connection attempt with basic info
        console.log(`WebSocket connection attempt from ${info.req.socket.remoteAddress}:${info.req.socket.remotePort}`);
        // Always accept at this stage, we'll authenticate after upgrade
        cb(true);
      }
    });
    this.clients = new Map(); // Map to store connected clients
    this.rooms = new Map(); // Map to store room participants
    
    this.initialize();
  }

  initialize() {
    this.wss.on('connection', (ws, req) => {
      this.handleConnection(ws, req);
    });

    // Listen for server errors
    this.wss.on('error', (error) => {
      console.error('WebSocket server error:', error);
    });

    // Listen for headers errors (happens before upgrade)
    this.wss.on('headers', (headers, req) => {
      console.debug(`WebSocket headers for ${req.socket.remoteAddress}: ${headers.length} headers`);
    });

    console.log('WebSocket server initialized on path /ws');
  }

  async handleConnection(ws, req) {
    // Generate a unique connection ID for tracking this connection in logs
    const connectionId = Math.random().toString(36).substring(2, 10);
    const clientIp = req.headers['x-forwarded-for'] || req.socket.remoteAddress;
    
    console.log(`[${connectionId}] New WebSocket connection from ${clientIp}, URL: ${req.url}`);
    
    // Set a timeout to close the connection if authentication takes too long
    const authTimeout = setTimeout(() => {
      console.warn(`[${connectionId}] Authentication timeout after 10s`);
      try {
        ws.close(1008, 'Authentication timeout');
      } catch (err) {
        console.error(`[${connectionId}] Error closing timed-out connection:`, err);
      }
    }, 10000);
    
    try {
      // Extract token from query string or headers
      const token = extractToken(req);
      
      if (!token) {
        console.warn(`[${connectionId}] No authentication token provided`);
        ws.close(1008, 'Authentication required');
        clearTimeout(authTimeout);
        return;
      }
      
      console.debug(`[${connectionId}] Token extracted, length: ${token.length}`);

      // Parse URL parameters for additional context
      const url = new URL(req.url, `http://${req.headers.host}`);
      const urlUserId = url.searchParams.get('userId');
      const urlUserType = url.searchParams.get('userType');
      
      console.debug(`[${connectionId}] URL params - userId: ${urlUserId || 'none'}, userType: ${urlUserType || 'none'}`);

      // ------------------------------------------------------------------
      // Verify JWT token against Cognito user pools (patient & nurse)
      // ------------------------------------------------------------------
      console.debug(`[${connectionId}] Verifying token...`);
      const { verifiedToken, userType, userId, email, username } = await verifyAndGetUser(token);
      console.log(`[${connectionId}] Token verified for user ${userId} (${userType})`);
      
      // Validate URL parameters match token claims
      if (urlUserId && urlUserId !== userId) {
        console.warn(`[${connectionId}] URL userId (${urlUserId}) doesn't match token subject (${userId})`);
        // We'll continue anyway but log the discrepancy
      }
      
      // Check if user session is valid
      console.debug(`[${connectionId}] Validating session for ${userId}...`);
      const sessionValid = await validateAndRefreshSession(userId, userType);
      
      if (!sessionValid) {
        console.warn(`[${connectionId}] Invalid session for ${userId} (${userType})`);
        ws.close(1008, 'Session expired');
        clearTimeout(authTimeout);
        return;
      }
      
      console.log(`[${connectionId}] Session validated for ${userId} (${userType})`);
      clearTimeout(authTimeout);

      // Store client information
      const clientInfo = {
        connectionId,
        userId,
        userType,
        email,
        username,
        ws,
        rooms: new Set(),
        connectedAt: new Date(),
        lastActivity: Date.now()
      };

      // Check if user already has an active connection
      if (this.clients.has(userId)) {
        const existingClient = this.clients.get(userId);
        console.log(`[${connectionId}] User ${userId} already has an active connection, closing previous`);
        
        try {
          // Close the existing connection with a replacement code
          existingClient.ws.close(1001, 'Connection replaced by newer session');
        } catch (err) {
          console.error(`[${connectionId}] Error closing previous connection:`, err);
        }
      }

      this.clients.set(userId, clientInfo);
      console.log(`[${connectionId}] Client registered: ${userId} (${userType}), total clients: ${this.clients.size}`);

      // Send welcome message
      try {
        ws.send(JSON.stringify({
          type: 'CONNECTION_SUCCESS',
          status: 'connected',
          userId,
          userType,
          email,
          username,
          connectionId
        }));
        console.debug(`[${connectionId}] Welcome message sent to ${userId}`);
      } catch (err) {
        console.error(`[${connectionId}] Error sending welcome message:`, err);
      }

      // Handle incoming messages
      ws.on('message', (data) => {
        try {
          // Update last activity timestamp
          clientInfo.lastActivity = Date.now();
          this.handleMessage(clientInfo, data);
        } catch (err) {
          console.error(`[${connectionId}] Error in message handler:`, err);
        }
      });

      // Handle client disconnect
      ws.on('close', (code, reason) => {
        console.log(`[${connectionId}] WebSocket closed: code=${code}, reason=${reason || 'none'}`);
        this.handleDisconnect(clientInfo);
      });

      // Handle errors
      ws.on('error', (error) => {
        console.error(`[${connectionId}] WebSocket error:`, error);
        this.handleDisconnect(clientInfo);
      });

      // Set up ping interval to keep connection alive
      const pingInterval = setInterval(() => {
        if (ws.readyState === WebSocket.OPEN) {
          try {
            // Send a ping message every 30 seconds
            ws.ping();
            console.debug(`[${connectionId}] Ping sent to ${userId}`);
          } catch (err) {
            console.error(`[${connectionId}] Error sending ping:`, err);
            clearInterval(pingInterval);
          }
        } else {
          // Clear interval if socket is closed
          clearInterval(pingInterval);
        }
      }, 30000);

      // Clean up ping interval on close
      ws.on('close', () => {
        clearInterval(pingInterval);
      });

    } catch (error) {
      console.error(`[${connectionId}] WebSocket connection error:`, error);
      
      // Send a more detailed error message to the client
      try {
        const errorMessage = {
          type: 'CONNECTION_ERROR',
          message: 'Authentication failed',
          details: process.env.NODE_ENV === 'development' ? error.message : 'Connection error'
        };
        
        ws.send(JSON.stringify(errorMessage));
        
        // Close with appropriate code
        let closeCode = 1008; // Policy violation (auth failure)
        let closeReason = 'Authentication failed';
        
        if (error.message.includes('token')) {
          closeReason = 'Invalid token';
        } else if (error.message.includes('session')) {
          closeReason = 'Session expired';
        } else if (error.message.includes('JWKS')) {
          closeCode = 1011; // Internal server error
          closeReason = 'Token verification service unavailable';
        }
        
        ws.close(closeCode, closeReason);
      } catch (sendError) {
        console.error(`[${connectionId}] Error sending error message:`, sendError);
        try {
          ws.close(1011, 'Internal server error');
        } catch (closeError) {
          console.error(`[${connectionId}] Error closing connection:`, closeError);
        }
      }
      
      clearTimeout(authTimeout);
    }
  }

  handleMessage(clientInfo, data) {
    const { connectionId, userId } = clientInfo;
    
    try {
      // Handle ping/pong messages to keep connection alive
      if (data.toString() === 'ping' || data.toString() === '{"type":"PING"}') {
        clientInfo.ws.send(JSON.stringify({ type: 'PONG', timestamp: Date.now() }));
        console.debug(`[${connectionId}] Received ping from ${userId}, sent pong`);
        return;
      }
      
      // Parse JSON message
      const message = JSON.parse(data);
      console.debug(`[${connectionId}] Received message type=${message.type} from ${userId}`);
      
      switch (message.type) {
        /* ------------ JOIN / LEAVE ------------ */
        case 'JOIN_CONVERSATION':
        case 'join_room': // legacy
          console.log(`[${connectionId}] User ${userId} joining conversation ${message.conversationId}`);
          this.joinRoom(clientInfo, message.conversationId);
          break;
        case 'LEAVE_CONVERSATION':
        case 'leave_room': // legacy
          console.log(`[${connectionId}] User ${userId} leaving conversation ${message.conversationId}`);
          this.leaveRoom(clientInfo, message.conversationId);
          break;

        /* ------------ TEXT MESSAGE ------------ */
        case 'TEXT_MESSAGE':
        case 'send_message': // legacy
          console.debug(`[${connectionId}] User ${userId} sending message to conversation ${message.conversationId}`);
          this.broadcastMessage(clientInfo, message);
          break;

        /* ------------ TYPING ------------------ */
        case 'TYPING_INDICATOR':
        case 'typing': // legacy
          console.debug(`[${connectionId}] User ${userId} typing in conversation ${message.conversationId}`);
          this.broadcastTyping(clientInfo, message);
          break;

        /* ------------ READ RECEIPT ------------ */
        case 'READ_RECEIPT':
        case 'read_receipt': // legacy
          console.debug(`[${connectionId}] User ${userId} marking messages read in conversation ${message.conversationId}`);
          this.broadcastReadReceipt(clientInfo, message);
          break;
          
        case 'PING':
          // Client-side ping, respond with pong
          clientInfo.ws.send(JSON.stringify({ type: 'PONG', timestamp: Date.now() }));
          console.debug(`[${connectionId}] Received PING from ${userId}, sent PONG`);
          break;
          
        default:
          console.warn(`[${connectionId}] Unknown message type from ${userId}: ${message.type}`);
          // Send error back to client
          try {
            clientInfo.ws.send(JSON.stringify({
              type: 'ERROR',
              error: `Unknown message type: ${message.type}`,
              timestamp: Date.now()
            }));
          } catch (err) {
            console.error(`[${connectionId}] Error sending error message:`, err);
          }
      }
    } catch (error) {
      console.error(`[${connectionId}] Error handling WebSocket message:`, error);
      // Try to send error back to client
      try {
        clientInfo.ws.send(JSON.stringify({
          type: 'ERROR',
          error: 'Failed to process message',
          details: process.env.NODE_ENV === 'development' ? error.message : 'Message processing error',
          timestamp: Date.now()
        }));
      } catch (sendError) {
        console.error(`[${connectionId}] Error sending error message:`, sendError);
      }
    }
  }

  joinRoom(clientInfo, conversationId) {
    const { connectionId, userId } = clientInfo;
    
    if (!conversationId) {
      console.error(`[${connectionId}] Invalid conversationId for join room`);
      return;
    }
    
    if (!this.rooms.has(conversationId)) {
      this.rooms.set(conversationId, new Set());
      console.debug(`[${connectionId}] Created new room: ${conversationId}`);
    }
    
    this.rooms.get(conversationId).add(userId);
    clientInfo.rooms.add(conversationId);
    
    console.log(`[${connectionId}] User ${userId} joined room ${conversationId}, participants: ${this.rooms.get(conversationId).size}`);
    
    // Notify other participants
    this.broadcastToRoom(conversationId, {
      type: 'user_joined',
      userId: clientInfo.userId,
      userType: clientInfo.userType,
      conversationId: conversationId,
      timestamp: new Date().toISOString()
    }, clientInfo.userId);
    
    // Confirm to the user that they joined
    try {
      clientInfo.ws.send(JSON.stringify({
        type: 'JOIN_CONFIRMATION',
        conversationId,
        timestamp: new Date().toISOString(),
        participantCount: this.rooms.get(conversationId).size
      }));
    } catch (err) {
      console.error(`[${connectionId}] Error sending join confirmation:`, err);
    }
  }

  leaveRoom(clientInfo, conversationId) {
    const { connectionId, userId } = clientInfo;
    
    if (!conversationId) {
      console.error(`[${connectionId}] Invalid conversationId for leave room`);
      return;
    }
    
    if (this.rooms.has(conversationId)) {
      this.rooms.get(conversationId).delete(userId);
      console.log(`[${connectionId}] User ${userId} left room ${conversationId}`);
      
      if (this.rooms.get(conversationId).size === 0) {
        this.rooms.delete(conversationId);
        console.debug(`[${connectionId}] Room ${conversationId} deleted (no participants)`);
      }
    }
    
    clientInfo.rooms.delete(conversationId);
    
    // Notify other participants
    this.broadcastToRoom(conversationId, {
      type: 'user_left',
      userId: clientInfo.userId,
      userType: clientInfo.userType,
      conversationId: conversationId,
      timestamp: new Date().toISOString()
    }, clientInfo.userId);
  }

  broadcastMessage(clientInfo, message) {
    const { connectionId, userId } = clientInfo;
    const { conversationId, content, messageType } = message;
    
    console.log(`[${connectionId}] Broadcasting message from ${userId} to conversation ${conversationId}`);
    
    // Use the existing room-based approach for bidirectional messaging
    const wsMessage = {
      type: 'TEXT_MESSAGE',
      conversationId: conversationId,
      senderId: userId,
      senderType: clientInfo.userType,
      content: content,
      messageType: messageType,
      timestamp: new Date().toISOString()
    };
    
    // Broadcast to room participants (this is the correct approach for bidirectional messaging)
    const recipientCount = this.broadcastToRoom(conversationId, wsMessage, userId);
    console.log(`[${connectionId}] Message broadcast to ${recipientCount} recipients in room ${conversationId}`);
  }

  broadcastTyping(clientInfo, message) {
    const { connectionId, userId } = clientInfo;
    const { conversationId, isTyping } = message;
    
    if (!conversationId) {
      console.error(`[${connectionId}] Missing conversationId in typing indicator`);
      return;
    }
    
    this.broadcastToRoom(conversationId, {
      type: 'TYPING_INDICATOR',
      conversationId,
      senderId: userId,
      senderType: clientInfo.userType,
      senderName: clientInfo.username,
      isTyping: isTyping !== false,
      timestamp: new Date().toISOString()
    }, userId);
  }

  broadcastReadReceipt(clientInfo, message) {
    const { connectionId, userId } = clientInfo;
    const { conversationId, messageId } = message;
    
    if (!conversationId) {
      console.error(`[${connectionId}] Missing conversationId in read receipt`);
      return;
    }
    
    if (!messageId) {
      console.error(`[${connectionId}] Missing messageId in read receipt`);
      return;
    }
    
    this.broadcastToRoom(conversationId, {
      type: 'READ_RECEIPT',
      conversationId,
      messageId,
      readBy: userId,
      readByName: clientInfo.username,
      readByType: clientInfo.userType,
      readAt: new Date().toISOString()
    });
  }

  broadcastToRoom(conversationId, message, excludeUserId = null) {
    if (!this.rooms.has(conversationId)) {
      return 0;
    }
    
    const participants = this.rooms.get(conversationId);
    let sentCount = 0;
    
    participants.forEach(userId => {
      if (userId === excludeUserId) {
        return;
      }
      
      const client = this.clients.get(userId);
      if (client && client.ws.readyState === WebSocket.OPEN) {
        try {
          client.ws.send(JSON.stringify(message));
          sentCount++;
        } catch (err) {
          console.error(`[${client.connectionId}] Error sending to ${userId}:`, err);
        }
      }
    });
    
    return sentCount;
  }

  handleDisconnect(clientInfo) {
    if (!clientInfo) return;
    
    const { connectionId, userId } = clientInfo;
    console.log(`[${connectionId}] Handling disconnect for ${userId}`);
    
    // Remove from all rooms
    clientInfo.rooms.forEach(conversationId => {
      this.leaveRoom(clientInfo, conversationId);
    });
    
    // Remove from clients map only if this is the current connection
    // (not if it was already replaced by a newer one)
    const currentClient = this.clients.get(userId);
    if (currentClient && currentClient.connectionId === connectionId) {
      this.clients.delete(userId);
      console.log(`[${connectionId}] Client ${userId} removed from active clients`);
    }
    
    console.log(`[${connectionId}] Disconnect complete for ${userId}, remaining clients: ${this.clients.size}`);
  }

  // Send message to specific user (for one-on-one communication)
  sendToUser(userId, message) {
    const client = this.clients.get(userId);
    if (client && client.ws.readyState === WebSocket.OPEN) {
      try {
        client.ws.send(JSON.stringify(message));
        console.debug(`Message sent to user ${userId}: ${message.type}`);
        return true;
      } catch (error) {
        console.error(`Error sending message to user ${userId}:`, error);
        return false;
      }
    } else {
      console.debug(`User ${userId} not connected or connection not ready`);
      return false;
    }
  }

  // Broadcast to all connected clients
  broadcastToAll(message) {
    let sentCount = 0;
    this.clients.forEach(client => {
      if (client.ws.readyState === WebSocket.OPEN) {
        try {
          client.ws.send(JSON.stringify(message));
          sentCount++;
        } catch (err) {
          console.error(`[${client.connectionId}] Error broadcasting to ${client.userId}:`, err);
        }
      }
    });
    console.log(`Broadcast message sent to ${sentCount} clients`);
    return sentCount;
  }

  // Get connected clients count
  getConnectedClientsCount() {
    return this.clients.size;
  }

  // Get room participants count
  getRoomParticipantsCount(conversationId) {
    return this.rooms.has(conversationId) ? this.rooms.get(conversationId).size : 0;
  }

  // Get user's active conversations
  getUserActiveConversations(userId) {
    const client = this.clients.get(userId);
    return client ? Array.from(client.rooms) : [];
  }
  
  // Get server stats
  getStats() {
    return {
      totalClients: this.clients.size,
      totalRooms: this.rooms.size,
      rooms: Array.from(this.rooms.entries()).map(([roomId, participants]) => ({
        roomId,
        participantCount: participants.size,
        participants: Array.from(participants)
      }))
    };
  }
}

module.exports = WebSocketManager;
