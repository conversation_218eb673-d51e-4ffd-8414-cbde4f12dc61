import { useState, useEffect, useRef, useCallback } from 'react';
import useWebSocket, { WebSocketMessage } from './useWebSocket';
import { 
  Conversation, 
  Message, 
  ConversationsResponse, 
  ConversationResponse, 
  MessagesResponse 
} from '@/store/api/chatApiSlice';

interface WebSocketChatDataState {
  conversations: Conversation[];
  conversationsLoading: boolean;
  conversationsError: string | null;
  
  currentConversation: Conversation | null;
  conversationLoading: boolean;
  conversationError: string | null;
  
  messages: Record<string, Message[]>;
  messagesLoading: Record<string, boolean>;
  messagesError: Record<string, string | null>;
  
  pendingRequests: Map<string, {
    resolve: (value: any) => void;
    reject: (error: any) => void;
    timestamp: number;
  }>;
}

interface UseWebSocketChatDataOptions {
  url: string;
  token: string;
  userId: string;
  userType: 'nurse' | 'patient';
  userName: string;
  enabled?: boolean;
}

const useWebSocketChatData = ({
  url,
  token,
  userId,
  userType,
  userName,
  enabled = true,
}: UseWebSocketChatDataOptions) => {
  const [state, setState] = useState<WebSocketChatDataState>({
    conversations: [],
    conversationsLoading: false,
    conversationsError: null,
    
    currentConversation: null,
    conversationLoading: false,
    conversationError: null,
    
    messages: {},
    messagesLoading: {},
    messagesError: {},
    
    pendingRequests: new Map(),
  });

  const requestIdCounter = useRef(0);
  const requestTimeoutRef = useRef<Record<string, NodeJS.Timeout>>({});

  // Generate unique request ID
  const generateRequestId = useCallback(() => {
    return `req_${Date.now()}_${++requestIdCounter.current}`;
  }, []);

  // Handle WebSocket messages
  const handleWebSocketMessage = useCallback((message: WebSocketMessage) => {
    console.debug('WebSocket chat data received message:', message.type, message);

    // Handle responses to our requests
    if (message.requestId && state.pendingRequests.has(message.requestId)) {
      const request = state.pendingRequests.get(message.requestId);
      if (request) {
        // Clear timeout
        if (requestTimeoutRef.current[message.requestId]) {
          clearTimeout(requestTimeoutRef.current[message.requestId]);
          delete requestTimeoutRef.current[message.requestId];
        }

        // Remove from pending requests
        setState(prev => {
          const newPendingRequests = new Map(prev.pendingRequests);
          newPendingRequests.delete(message.requestId!);
          return { ...prev, pendingRequests: newPendingRequests };
        });

        // Resolve or reject the promise
        if (message.success) {
          request.resolve(message.data || message);
        } else {
          request.reject(new Error(message.error || 'Request failed'));
        }
        return;
      }
    }

    // Handle real-time updates
    switch (message.type) {
      case 'CONVERSATIONS_RESPONSE':
        setState(prev => ({
          ...prev,
          conversations: message.conversations || [],
          conversationsLoading: false,
          conversationsError: null,
        }));
        break;

      case 'CONVERSATION_RESPONSE':
        setState(prev => ({
          ...prev,
          currentConversation: message.conversation || null,
          conversationLoading: false,
          conversationError: null,
        }));
        break;

      case 'MESSAGES_RESPONSE':
        if (message.conversationId) {
          setState(prev => ({
            ...prev,
            messages: {
              ...prev.messages,
              [message.conversationId!]: message.messages || [],
            },
            messagesLoading: {
              ...prev.messagesLoading,
              [message.conversationId!]: false,
            },
            messagesError: {
              ...prev.messagesError,
              [message.conversationId!]: null,
            },
          }));
        }
        break;

      case 'CONVERSATION_CREATED':
        if (message.conversation) {
          setState(prev => ({
            ...prev,
            conversations: [message.conversation!, ...prev.conversations],
            currentConversation: message.conversation!,
          }));
        }
        break;

      case 'new_message':
        // Handle real-time new messages
        if (message.conversationId && message.content) {
          const newMessage: Message = {
            id: message.metadata?.messageId as string || `temp_${Date.now()}`,
            conversationId: message.conversationId,
            senderId: message.senderId || '',
            senderType: message.senderType || 'patient',
            senderName: message.senderName || '',
            content: message.content,
            type: 'text',
            status: 'sent',
            timestamp: message.timestamp || new Date().toISOString(),
            metadata: message.metadata,
          };

          setState(prev => ({
            ...prev,
            messages: {
              ...prev.messages,
              [message.conversationId!]: [
                ...(prev.messages[message.conversationId!] || []),
                newMessage,
              ],
            },
          }));
        }
        break;

      case 'ERROR_RESPONSE':
        console.error('WebSocket chat error:', message.error);
        break;

      default:
        // Handle other message types (typing, read receipts, etc.)
        break;
    }
  }, [state.pendingRequests]);

  // Initialize WebSocket
  const webSocket = useWebSocket({
    url,
    token,
    userId,
    userType,
    userName,
    enabled,
    onMessage: handleWebSocketMessage,
  });

  // Send request with promise-based response
  const sendRequest = useCallback((message: WebSocketMessage, timeoutMs = 10000): Promise<any> => {
    return new Promise((resolve, reject) => {
      if (!webSocket.status.connected) {
        reject(new Error('WebSocket not connected'));
        return;
      }

      const requestId = generateRequestId();
      const messageWithId = { ...message, requestId };

      // Store the request
      setState(prev => {
        const newPendingRequests = new Map(prev.pendingRequests);
        newPendingRequests.set(requestId, { resolve, reject, timestamp: Date.now() });
        return { ...prev, pendingRequests: newPendingRequests };
      });

      // Set timeout
      requestTimeoutRef.current[requestId] = setTimeout(() => {
        setState(prev => {
          const newPendingRequests = new Map(prev.pendingRequests);
          newPendingRequests.delete(requestId);
          return { ...prev, pendingRequests: newPendingRequests };
        });
        delete requestTimeoutRef.current[requestId];
        reject(new Error('Request timeout'));
      }, timeoutMs);

      // Send the message
      const success = webSocket.sendMessage && webSocket.sendMessage(messageWithId);
      if (!success) {
        // Clean up on send failure
        setState(prev => {
          const newPendingRequests = new Map(prev.pendingRequests);
          newPendingRequests.delete(requestId);
          return { ...prev, pendingRequests: newPendingRequests };
        });
        if (requestTimeoutRef.current[requestId]) {
          clearTimeout(requestTimeoutRef.current[requestId]);
          delete requestTimeoutRef.current[requestId];
        }
        reject(new Error('Failed to send WebSocket message'));
      }
    });
  }, [webSocket.status.connected, webSocket.sendMessage, generateRequestId]);

  // Clean up timeouts on unmount
  useEffect(() => {
    return () => {
      Object.values(requestTimeoutRef.current).forEach(timeout => clearTimeout(timeout));
    };
  }, []);

  return {
    ...state,
    webSocketStatus: webSocket.status,
    sendRequest,
    joinConversation: webSocket.joinConversation,
    leaveConversation: webSocket.leaveConversation,
    sendTextMessage: webSocket.sendTextMessage,
    sendTypingIndicator: webSocket.sendTypingIndicator,
    sendReadReceipt: webSocket.sendReadReceipt,
    typingUsers: webSocket.typingUsers,
  };
};

export default useWebSocketChatData;
